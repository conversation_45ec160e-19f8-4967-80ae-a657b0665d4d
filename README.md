# Discord Screenshot Bot

Bot Discord có thể chụp màn hình máy tính và gửi ảnh qua Discord.

## 🚀 Tính năng

- ✅ Chụp màn hình bằng slash command `/screenshot`
- ✅ Hỗ trợ nhiều màn hình
- ✅ Gửi ảnh trực tiếp qua Discord
- ✅ Tự động xóa file tạm thời
- ✅ Xử lý lỗi chi tiết

## 📋 Yêu cầu

- Node.js 16.9.0 trở lên
- Discord Bot Token

## 🛠️ Cài đặt

1. **Clone hoặc tải project**

2. **Cài đặt dependencies:**
   ```bash
   npm install
   ```

3. **Tạo Discord Bot:**
   - T<PERSON>y cập [Discord Developer Portal](https://discord.com/developers/applications)
   - Tạo New Application
   - Vào tab "Bot" và tạo bot
   - Copy Bot Token

4. **Cấu hình bot:**
   - Mở file `.env`
   - Thay `YOUR_BOT_TOKEN_HERE` bằng Bot Token của bạn
   - (<PERSON><PERSON><PERSON> chọn) Thêm GUILD_ID nếu muốn bot chỉ hoạt động trong server cụ thể

5. **M<PERSON>i bot vào server:**
   - Vào tab "OAuth2" > "URL Generator"
   - Chọn scope: `bot` và `applications.commands`
   - Chọn permissions: `Send Messages`, `Use Slash Commands`, `Attach Files`
   - Copy URL và mời bot vào server

## 🎮 Sử dụng

### Lệnh có sẵn:

- `/screenshot` - Chụp màn hình chính
- `/screenshot display:0` - Chụp màn hình số 0
- `/screenshot display:1` - Chụp màn hình số 1 (nếu có)

## 🏃‍♂️ Chạy bot

```bash
npm start
```

hoặc

```bash
node index.js
```

## 📁 Cấu trúc project

```
discordScreen/
├── index.js          # File chính của bot
├── package.json      # Dependencies và scripts
├── .env             # Cấu hình bot (token)
└── README.md        # Hướng dẫn này
```

## ⚠️ Lưu ý

- Bot cần quyền truy cập màn hình trên máy tính
- File ảnh tạm thời sẽ được tự động xóa sau 5 giây
- Slash commands có thể mất 1 giờ để có hiệu lực nếu đăng ký global
- Để commands có hiệu lực ngay lập tức, hãy thêm GUILD_ID vào file .env

## 🐛 Xử lý lỗi

Nếu gặp lỗi:

1. **"Không tìm thấy DISCORD_TOKEN"**
   - Kiểm tra file .env có đúng token không

2. **"Không thể đăng nhập bot"**
   - Kiểm tra token có đúng không
   - Kiểm tra bot có được enable không

3. **"Lỗi khi chụp màn hình"**
   - Kiểm tra quyền truy cập màn hình
   - Thử chạy với quyền admin (Windows)

## 📞 Hỗ trợ

Nếu cần hỗ trợ, hãy kiểm tra console log để xem lỗi chi tiết.
