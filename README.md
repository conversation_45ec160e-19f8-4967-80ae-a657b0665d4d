# Discord Remote Control Bot

Bot Discord có thể chụp màn hình và điều khiển máy tính từ xa qua Discord.

## 🚀 Tính năng

### 📸 Chụp màn hình
- ✅ Chụp màn hình bằng slash command `/screenshot`
- ✅ Hỗ trợ nhiều màn hình
- ✅ Gửi ảnh trực tiếp qua Discord
- ✅ Tự động xóa file tạm thời

### 💻 Điều khiển máy tính
- ✅ Tắt máy tính `/shutdown`
- ✅ Khởi động lại máy tính `/restart`
- ✅ Chạy lệnh command line `/cmd`
- ✅ Xem thông tin hệ thống `/sysinfo`
- ✅ Điều khiển âm lượng `/volume`
- ✅ Quản lý tiến trình `/process`

### 🛡️ Bảo mật
- ✅ Chặn các lệnh nguy hiểm
- ✅ Thời gian delay tối thiểu cho shutdown/restart
- ✅ <PERSON><PERSON> lý lỗi chi tiết và an toàn

## 📋 Yêu cầu

- Node.js 16.9.0 trở lên
- Discord Bot Token

## 🛠️ Cài đặt

1. **Clone hoặc tải project**

2. **Cài đặt dependencies:**
   ```bash
   npm install
   ```

3. **Tạo Discord Bot:**
   - Truy cập [Discord Developer Portal](https://discord.com/developers/applications)
   - Tạo New Application
   - Vào tab "Bot" và tạo bot
   - Copy Bot Token

4. **Cấu hình bot:**
   - Mở file `.env`
   - Thay `YOUR_BOT_TOKEN_HERE` bằng Bot Token của bạn
   - (Tùy chọn) Thêm GUILD_ID nếu muốn bot chỉ hoạt động trong server cụ thể

5. **Mời bot vào server:**
   - Vào tab "OAuth2" > "URL Generator"
   - Chọn scope: `bot` và `applications.commands`
   - Chọn permissions: `Send Messages`, `Use Slash Commands`, `Attach Files`
   - Copy URL và mời bot vào server

## 🎮 Sử dụng

### 📸 Lệnh chụp màn hình:
- `/screenshot` - Chụp màn hình chính
- `/screenshot display:0` - Chụp màn hình số 0
- `/screenshot display:1` - Chụp màn hình số 1 (nếu có)

### 💻 Lệnh điều khiển máy tính:
- `/shutdown` - Tắt máy tính (delay mặc định 10 giây)
- `/shutdown delay:30` - Tắt máy sau 30 giây
- `/restart` - Khởi động lại máy tính (delay mặc định 10 giây)
- `/restart delay:60` - Khởi động lại sau 60 giây

### 🔧 Lệnh hệ thống:
- `/sysinfo` - Hiển thị thông tin hệ thống
- `/cmd command:dir` - Chạy lệnh "dir"
- `/cmd command:ipconfig` - Xem cấu hình mạng

### 🔊 Lệnh âm thanh:
- `/volume action:mute` - Tắt tiếng
- `/volume action:unmute` - Bật tiếng
- `/volume action:up` - Tăng âm lượng
- `/volume action:down` - Giảm âm lượng

### 📋 Lệnh tiến trình:
- `/process action:list` - Liệt kê tất cả tiến trình
- `/process action:kill name:notepad.exe` - Kết thúc tiến trình notepad

## 🏃‍♂️ Chạy bot

```bash
npm start
```

hoặc

```bash
node index.js
```

## 📁 Cấu trúc project

```
discordScreen/
├── index.js          # File chính của bot
├── package.json      # Dependencies và scripts
├── .env             # Cấu hình bot (token)
└── README.md        # Hướng dẫn này
```

## ⚠️ Lưu ý quan trọng

### 🔒 Bảo mật:
- **QUAN TRỌNG**: Bot này có thể điều khiển máy tính của bạn! Chỉ mời bot vào server riêng tư
- Không chia sẻ bot token với ai khác
- Chỉ cấp quyền sử dụng bot cho những người tin tưởng
- Bot có thể tắt máy, chạy lệnh, và truy cập hệ thống

### 🛡️ Các biện pháp bảo vệ đã tích hợp:
- Chặn các lệnh nguy hiểm như `format`, `del`, `rm -rf`
- Thời gian delay tối thiểu 5 giây cho shutdown/restart
- Timeout 10 giây cho các lệnh command line
- Giới hạn độ dài output để tránh spam

### 💻 Yêu cầu hệ thống:
- Bot cần quyền truy cập màn hình trên máy tính
- Trên Windows có thể cần chạy với quyền Administrator
- File ảnh tạm thời sẽ được tự động xóa sau 5 giây
- Slash commands có thể mất 1 giờ để có hiệu lực nếu đăng ký global
- Để commands có hiệu lực ngay lập tức, hãy thêm GUILD_ID vào file .env

## 🐛 Xử lý lỗi

Nếu gặp lỗi:

1. **"Không tìm thấy DISCORD_TOKEN"**
   - Kiểm tra file .env có đúng token không

2. **"Không thể đăng nhập bot"**
   - Kiểm tra token có đúng không
   - Kiểm tra bot có được enable không

3. **"Lỗi khi chụp màn hình"**
   - Kiểm tra quyền truy cập màn hình
   - Thử chạy với quyền admin (Windows)

## 📞 Hỗ trợ

Nếu cần hỗ trợ, hãy kiểm tra console log để xem lỗi chi tiết.
