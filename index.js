const { Client, GatewayIntentBits, SlashCommandBuilder, AttachmentBuilder, EmbedBuilder } = require('discord.js');
const screenshot = require('screenshot-desktop');
const fs = require('fs');
const path = require('path');
const { exec, spawn } = require('child_process');
const os = require('os');
const util = require('util');
require('dotenv').config();

const execAsync = util.promisify(exec);

// Tạo Discord client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

// Khi bot sẵn sàng
client.once('ready', async () => {
    console.log(`✅ Bot đã sẵn sàng! Đăng nhập với tên: ${client.user.tag}`);

    // Đăng ký slash commands
    const commands = [
        new SlashCommandBuilder()
            .setName('screenshot')
            .setDescription('Chụp màn hình máy tính và gửi ảnh')
            .addStringOption(option =>
                option.setName('display')
                    .setDescription('Chọn màn hình (để trống để chụp màn hình chính)')
                    .setRequired(false)
            ),

        new SlashCommandBuilder()
            .setName('shutdown')
            .setDescription('Tắt máy tính')
            .addIntegerOption(option =>
                option.setName('delay')
                    .setDescription('Thời gian delay (giây) trước khi tắt máy (mặc định: 10)')
                    .setRequired(false)
            ),

        new SlashCommandBuilder()
            .setName('restart')
            .setDescription('Khởi động lại máy tính')
            .addIntegerOption(option =>
                option.setName('delay')
                    .setDescription('Thời gian delay (giây) trước khi restart (mặc định: 10)')
                    .setRequired(false)
            ),

        new SlashCommandBuilder()
            .setName('cmd')
            .setDescription('Chạy lệnh command line')
            .addStringOption(option =>
                option.setName('command')
                    .setDescription('Lệnh cần chạy')
                    .setRequired(true)
            ),

        new SlashCommandBuilder()
            .setName('sysinfo')
            .setDescription('Hiển thị thông tin hệ thống'),

        new SlashCommandBuilder()
            .setName('volume')
            .setDescription('Điều khiển âm lượng')
            .addStringOption(option =>
                option.setName('action')
                    .setDescription('Hành động')
                    .setRequired(true)
                    .addChoices(
                        { name: 'Tắt tiếng', value: 'mute' },
                        { name: 'Bật tiếng', value: 'unmute' },
                        { name: 'Tăng âm lượng', value: 'up' },
                        { name: 'Giảm âm lượng', value: 'down' }
                    )
            ),

        new SlashCommandBuilder()
            .setName('process')
            .setDescription('Quản lý tiến trình')
            .addStringOption(option =>
                option.setName('action')
                    .setDescription('Hành động')
                    .setRequired(true)
                    .addChoices(
                        { name: 'Liệt kê tiến trình', value: 'list' },
                        { name: 'Kết thúc tiến trình', value: 'kill' }
                    )
            )
            .addStringOption(option =>
                option.setName('name')
                    .setDescription('Tên tiến trình (chỉ cần khi kill)')
                    .setRequired(false)
            )
    ];

    try {
        console.log('🔄 Đang đăng ký slash commands...');

        if (process.env.GUILD_ID) {
            // Đăng ký cho server cụ thể (nhanh hơn)
            const guild = client.guilds.cache.get(process.env.GUILD_ID);
            await guild.commands.set(commands);
            console.log('✅ Đã đăng ký commands cho server cụ thể');
        } else {
            // Đăng ký global (có thể mất 1 giờ để có hiệu lực)
            await client.application.commands.set(commands);
            console.log('✅ Đã đăng ký global commands');
        }
    } catch (error) {
        console.error('❌ Lỗi khi đăng ký commands:', error);
    }
});

// Xử lý slash commands
client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    switch (interaction.commandName) {
        case 'screenshot':
            await handleScreenshotCommand(interaction);
            break;
        case 'shutdown':
            await handleShutdownCommand(interaction);
            break;
        case 'restart':
            await handleRestartCommand(interaction);
            break;
        case 'cmd':
            await handleCmdCommand(interaction);
            break;
        case 'sysinfo':
            await handleSysInfoCommand(interaction);
            break;
        case 'volume':
            await handleVolumeCommand(interaction);
            break;
        case 'process':
            await handleProcessCommand(interaction);
            break;
        default:
            await interaction.reply({ content: '❌ Lệnh không được hỗ trợ!', ephemeral: true });
    }
});

// Hàm xử lý lệnh screenshot
async function handleScreenshotCommand(interaction) {
    try {
        // Defer reply để có thời gian xử lý
        await interaction.deferReply();

        console.log('📸 Đang chụp màn hình...');

        // Chụp màn hình
        const displayOption = interaction.options.getString('display');
        let screenshotOptions = { format: 'png' };

        if (displayOption) {
            screenshotOptions.screen = parseInt(displayOption) || 0;
        }

        const imgBuffer = await screenshot(screenshotOptions);

        // Tạo tên file với timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `screenshot-${timestamp}.png`;
        const filepath = path.join(__dirname, filename);

        // Lưu file tạm thời
        fs.writeFileSync(filepath, imgBuffer);

        // Tạo attachment để gửi
        const attachment = new AttachmentBuilder(filepath, { name: filename });

        // Gửi ảnh
        await interaction.editReply({
            content: '📸 **Chụp màn hình thành công!**',
            files: [attachment]
        });

        console.log('✅ Đã gửi screenshot thành công');

        // Xóa file tạm thời sau 5 giây
        setTimeout(() => {
            try {
                fs.unlinkSync(filepath);
                console.log('🗑️ Đã xóa file tạm thời');
            } catch (err) {
                console.error('⚠️ Không thể xóa file tạm thời:', err.message);
            }
        }, 5000);

    } catch (error) {
        console.error('❌ Lỗi khi chụp màn hình:', error);

        const errorMessage = '❌ **Lỗi khi chụp màn hình!**\n' +
                           `Lỗi: ${error.message}\n\n` +
                           '💡 **Gợi ý:**\n' +
                           '• Kiểm tra quyền truy cập màn hình\n' +
                           '• Thử lại sau vài giây\n' +
                           '• Liên hệ admin nếu lỗi tiếp tục';

        try {
            if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        } catch (replyError) {
            console.error('❌ Lỗi khi gửi thông báo lỗi:', replyError);
        }
    }
}

// Hàm xử lý lệnh shutdown
async function handleShutdownCommand(interaction) {
    try {
        await interaction.deferReply();

        const delay = interaction.options.getInteger('delay') || 10;

        if (delay < 5) {
            await interaction.editReply({
                content: '❌ **Thời gian delay tối thiểu là 5 giây để đảm bảo an toàn!**'
            });
            return;
        }

        const embed = new EmbedBuilder()
            .setColor('#FF0000')
            .setTitle('🔴 CẢNH BÁO: TẮT MÁY TÍNH')
            .setDescription(`Máy tính sẽ được tắt sau **${delay} giây**`)
            .addFields(
                { name: '⏰ Thời gian còn lại', value: `${delay} giây`, inline: true },
                { name: '💻 Hệ thống', value: os.platform(), inline: true }
            )
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });

        // Thực hiện shutdown
        const shutdownCmd = os.platform() === 'win32'
            ? `shutdown /s /t ${delay}`
            : `sudo shutdown -h +${Math.ceil(delay/60)}`;

        exec(shutdownCmd, (error) => {
            if (error) {
                console.error('❌ Lỗi khi tắt máy:', error);
            } else {
                console.log('🔴 Lệnh tắt máy đã được thực hiện');
            }
        });

    } catch (error) {
        console.error('❌ Lỗi shutdown command:', error);
        await interaction.editReply({
            content: `❌ **Lỗi khi thực hiện lệnh tắt máy!**\nLỗi: ${error.message}`
        });
    }
}

// Hàm xử lý lệnh restart
async function handleRestartCommand(interaction) {
    try {
        await interaction.deferReply();

        const delay = interaction.options.getInteger('delay') || 10;

        if (delay < 5) {
            await interaction.editReply({
                content: '❌ **Thời gian delay tối thiểu là 5 giây để đảm bảo an toàn!**'
            });
            return;
        }

        const embed = new EmbedBuilder()
            .setColor('#FFA500')
            .setTitle('🔄 CẢNH BÁO: KHỞI ĐỘNG LẠI MÁY TÍNH')
            .setDescription(`Máy tính sẽ được khởi động lại sau **${delay} giây**`)
            .addFields(
                { name: '⏰ Thời gian còn lại', value: `${delay} giây`, inline: true },
                { name: '💻 Hệ thống', value: os.platform(), inline: true }
            )
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });

        // Thực hiện restart
        const restartCmd = os.platform() === 'win32'
            ? `shutdown /r /t ${delay}`
            : `sudo shutdown -r +${Math.ceil(delay/60)}`;

        exec(restartCmd, (error) => {
            if (error) {
                console.error('❌ Lỗi khi restart:', error);
            } else {
                console.log('🔄 Lệnh restart đã được thực hiện');
            }
        });

    } catch (error) {
        console.error('❌ Lỗi restart command:', error);
        await interaction.editReply({
            content: `❌ **Lỗi khi thực hiện lệnh restart!**\nLỗi: ${error.message}`
        });
    }
}

// Hàm xử lý lệnh cmd
async function handleCmdCommand(interaction) {
    try {
        await interaction.deferReply();

        const command = interaction.options.getString('command');

        // Kiểm tra lệnh nguy hiểm
        const dangerousCommands = ['format', 'del', 'rm -rf', 'rmdir /s', 'shutdown', 'restart'];
        const isDangerous = dangerousCommands.some(cmd => command.toLowerCase().includes(cmd));

        if (isDangerous) {
            await interaction.editReply({
                content: '❌ **Lệnh này có thể nguy hiểm và đã bị chặn!**\nSử dụng các lệnh chuyên dụng như `/shutdown` hoặc `/restart` thay thế.'
            });
            return;
        }

        console.log(`🔧 Thực hiện lệnh: ${command}`);

        exec(command, { timeout: 10000 }, async (error, stdout, stderr) => {
            let result = '';

            if (stdout) result += `**Output:**\n\`\`\`\n${stdout}\n\`\`\`\n`;
            if (stderr) result += `**Error:**\n\`\`\`\n${stderr}\n\`\`\`\n`;
            if (error) result += `**Execution Error:**\n\`\`\`\n${error.message}\n\`\`\``;

            if (!result) result = '✅ **Lệnh đã thực hiện thành công (không có output)**';

            // Giới hạn độ dài output
            if (result.length > 1900) {
                result = result.substring(0, 1900) + '\n\n... (output bị cắt do quá dài)';
            }

            const embed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('💻 Kết quả Command Line')
                .addFields(
                    { name: '📝 Lệnh đã chạy', value: `\`${command}\``, inline: false },
                    { name: '📤 Kết quả', value: result || 'Không có output', inline: false }
                )
                .setTimestamp();

            await interaction.editReply({ embeds: [embed] });
        });

    } catch (error) {
        console.error('❌ Lỗi cmd command:', error);
        await interaction.editReply({
            content: `❌ **Lỗi khi thực hiện lệnh!**\nLỗi: ${error.message}`
        });
    }
}

// Hàm xử lý lệnh sysinfo
async function handleSysInfoCommand(interaction) {
    try {
        await interaction.deferReply();

        const systemInfo = {
            platform: os.platform(),
            arch: os.arch(),
            hostname: os.hostname(),
            uptime: Math.floor(os.uptime() / 3600), // giờ
            totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024), // GB
            freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024), // GB
            cpus: os.cpus().length,
            cpuModel: os.cpus()[0]?.model || 'Unknown'
        };

        const embed = new EmbedBuilder()
            .setColor('#0099FF')
            .setTitle('💻 Thông tin hệ thống')
            .addFields(
                { name: '🖥️ Hệ điều hành', value: systemInfo.platform, inline: true },
                { name: '🏗️ Kiến trúc', value: systemInfo.arch, inline: true },
                { name: '🏠 Hostname', value: systemInfo.hostname, inline: true },
                { name: '⏰ Uptime', value: `${systemInfo.uptime} giờ`, inline: true },
                { name: '🧠 CPU', value: `${systemInfo.cpus} cores`, inline: true },
                { name: '💾 RAM', value: `${systemInfo.freeMemory}GB / ${systemInfo.totalMemory}GB`, inline: true },
                { name: '🔧 CPU Model', value: systemInfo.cpuModel.substring(0, 50), inline: false }
            )
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('❌ Lỗi sysinfo command:', error);
        await interaction.editReply({
            content: `❌ **Lỗi khi lấy thông tin hệ thống!**\nLỗi: ${error.message}`
        });
    }
}

// Hàm xử lý lệnh volume
async function handleVolumeCommand(interaction) {
    try {
        await interaction.deferReply();

        const action = interaction.options.getString('action');
        let command = '';
        let message = '';

        if (os.platform() === 'win32') {
            switch (action) {
                case 'mute':
                    command = 'powershell -Command "(New-Object -comObject WScript.Shell).SendKeys([char]173)"';
                    message = '🔇 Đã tắt tiếng';
                    break;
                case 'unmute':
                    command = 'powershell -Command "(New-Object -comObject WScript.Shell).SendKeys([char]173)"';
                    message = '🔊 Đã bật tiếng';
                    break;
                case 'up':
                    command = 'powershell -Command "(New-Object -comObject WScript.Shell).SendKeys([char]175)"';
                    message = '🔊 Đã tăng âm lượng';
                    break;
                case 'down':
                    command = 'powershell -Command "(New-Object -comObject WScript.Shell).SendKeys([char]174)"';
                    message = '🔉 Đã giảm âm lượng';
                    break;
            }
        } else {
            await interaction.editReply({
                content: '❌ **Tính năng điều khiển âm lượng chỉ hỗ trợ Windows!**'
            });
            return;
        }

        exec(command, (error) => {
            if (error) {
                console.error('❌ Lỗi volume command:', error);
            } else {
                console.log(`🔊 Volume action: ${action}`);
            }
        });

        const embed = new EmbedBuilder()
            .setColor('#00FF00')
            .setTitle('🔊 Điều khiển âm lượng')
            .setDescription(message)
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        console.error('❌ Lỗi volume command:', error);
        await interaction.editReply({
            content: `❌ **Lỗi khi điều khiển âm lượng!**\nLỗi: ${error.message}`
        });
    }
}

// Hàm xử lý lệnh process
async function handleProcessCommand(interaction) {
    try {
        await interaction.deferReply();

        const action = interaction.options.getString('action');

        if (action === 'list') {
            const command = os.platform() === 'win32'
                ? 'tasklist /fo csv | findstr /v "Image"'
                : 'ps aux';

            exec(command, (error, stdout, stderr) => {
                if (error) {
                    interaction.editReply({
                        content: `❌ **Lỗi khi liệt kê tiến trình!**\nLỗi: ${error.message}`
                    });
                    return;
                }

                let processes = stdout;
                if (processes.length > 1900) {
                    processes = processes.substring(0, 1900) + '\n\n... (danh sách bị cắt do quá dài)';
                }

                const embed = new EmbedBuilder()
                    .setColor('#0099FF')
                    .setTitle('📋 Danh sách tiến trình')
                    .setDescription(`\`\`\`\n${processes}\n\`\`\``)
                    .setTimestamp();

                interaction.editReply({ embeds: [embed] });
            });

        } else if (action === 'kill') {
            const processName = interaction.options.getString('name');

            if (!processName) {
                await interaction.editReply({
                    content: '❌ **Vui lòng nhập tên tiến trình cần kết thúc!**'
                });
                return;
            }

            const command = os.platform() === 'win32'
                ? `taskkill /f /im ${processName}`
                : `pkill ${processName}`;

            exec(command, async (error, stdout, stderr) => {
                let message = '';

                if (error) {
                    message = `❌ **Không thể kết thúc tiến trình "${processName}"**\nLỗi: ${error.message}`;
                } else {
                    message = `✅ **Đã kết thúc tiến trình "${processName}" thành công!**`;
                }

                await interaction.editReply({ content: message });
            });
        }

    } catch (error) {
        console.error('❌ Lỗi process command:', error);
        await interaction.editReply({
            content: `❌ **Lỗi khi quản lý tiến trình!**\nLỗi: ${error.message}`
        });
    }
}

// Xử lý lỗi
client.on('error', error => {
    console.error('❌ Discord client error:', error);
});

process.on('unhandledRejection', error => {
    console.error('❌ Unhandled promise rejection:', error);
});

// Đăng nhập bot
const token = process.env.DISCORD_TOKEN;
if (!token) {
    console.error('❌ Không tìm thấy DISCORD_TOKEN trong file .env!');
    console.log('💡 Hãy thêm token bot vào file .env');
    process.exit(1);
}

client.login(token).catch(error => {
    console.error('❌ Không thể đăng nhập bot:', error.message);
    console.log('💡 Kiểm tra lại DISCORD_TOKEN trong file .env');
});
