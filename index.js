const { Client, GatewayIntentBits, SlashCommandBuilder, AttachmentBuilder } = require('discord.js');
const screenshot = require('screenshot-desktop');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Tạo Discord client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

// Khi bot sẵn sàng
client.once('ready', async () => {
    console.log(`✅ Bot đã sẵn sàng! Đăng nhập với tên: ${client.user.tag}`);
    
    // Đăng ký slash commands
    const commands = [
        new SlashCommandBuilder()
            .setName('screenshot')
            .setDescription('Chụp màn hình máy tính và gửi ảnh')
            .addStringOption(option =>
                option.setName('display')
                    .setDescription('Chọn màn hình (để trống để chụp màn hình chính)')
                    .setRequired(false)
            )
    ];

    try {
        console.log('🔄 Đang đăng ký slash commands...');
        
        if (process.env.GUILD_ID) {
            // Đăng ký cho server cụ thể (nhanh hơn)
            const guild = client.guilds.cache.get(process.env.GUILD_ID);
            await guild.commands.set(commands);
            console.log('✅ Đã đăng ký commands cho server cụ thể');
        } else {
            // Đăng ký global (có thể mất 1 giờ để có hiệu lực)
            await client.application.commands.set(commands);
            console.log('✅ Đã đăng ký global commands');
        }
    } catch (error) {
        console.error('❌ Lỗi khi đăng ký commands:', error);
    }
});

// Xử lý slash commands
client.on('interactionCreate', async interaction => {
    if (!interaction.isChatInputCommand()) return;

    if (interaction.commandName === 'screenshot') {
        await handleScreenshotCommand(interaction);
    }
});

// Hàm xử lý lệnh screenshot
async function handleScreenshotCommand(interaction) {
    try {
        // Defer reply để có thời gian xử lý
        await interaction.deferReply();

        console.log('📸 Đang chụp màn hình...');
        
        // Chụp màn hình
        const displayOption = interaction.options.getString('display');
        let screenshotOptions = { format: 'png' };
        
        if (displayOption) {
            screenshotOptions.screen = parseInt(displayOption) || 0;
        }

        const imgBuffer = await screenshot(screenshotOptions);
        
        // Tạo tên file với timestamp
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `screenshot-${timestamp}.png`;
        const filepath = path.join(__dirname, filename);
        
        // Lưu file tạm thời
        fs.writeFileSync(filepath, imgBuffer);
        
        // Tạo attachment để gửi
        const attachment = new AttachmentBuilder(filepath, { name: filename });
        
        // Gửi ảnh
        await interaction.editReply({
            content: '📸 **Chụp màn hình thành công!**',
            files: [attachment]
        });
        
        console.log('✅ Đã gửi screenshot thành công');
        
        // Xóa file tạm thời sau 5 giây
        setTimeout(() => {
            try {
                fs.unlinkSync(filepath);
                console.log('🗑️ Đã xóa file tạm thời');
            } catch (err) {
                console.error('⚠️ Không thể xóa file tạm thời:', err.message);
            }
        }, 5000);
        
    } catch (error) {
        console.error('❌ Lỗi khi chụp màn hình:', error);
        
        const errorMessage = '❌ **Lỗi khi chụp màn hình!**\n' +
                           `Lỗi: ${error.message}\n\n` +
                           '💡 **Gợi ý:**\n' +
                           '• Kiểm tra quyền truy cập màn hình\n' +
                           '• Thử lại sau vài giây\n' +
                           '• Liên hệ admin nếu lỗi tiếp tục';
        
        try {
            if (interaction.deferred) {
                await interaction.editReply({ content: errorMessage });
            } else {
                await interaction.reply({ content: errorMessage, ephemeral: true });
            }
        } catch (replyError) {
            console.error('❌ Lỗi khi gửi thông báo lỗi:', replyError);
        }
    }
}

// Xử lý lỗi
client.on('error', error => {
    console.error('❌ Discord client error:', error);
});

process.on('unhandledRejection', error => {
    console.error('❌ Unhandled promise rejection:', error);
});

// Đăng nhập bot
const token = process.env.DISCORD_TOKEN;
if (!token) {
    console.error('❌ Không tìm thấy DISCORD_TOKEN trong file .env!');
    console.log('💡 Hãy thêm token bot vào file .env');
    process.exit(1);
}

client.login(token).catch(error => {
    console.error('❌ Không thể đăng nhập bot:', error.message);
    console.log('💡 Kiểm tra lại DISCORD_TOKEN trong file .env');
});
