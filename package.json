{"name": "discordscreen", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["discord", "bot", "screenshot", "discord.js"], "author": "", "license": "ISC", "type": "commonjs", "description": "Discord bot có thể chụp màn hình và gửi <PERSON>nh qua Discord", "dependencies": {"child_process": "^1.0.2", "discord.js": "^14.19.3", "dotenv": "^16.5.0", "os": "^0.1.2", "screenshot-desktop": "^1.15.1", "util": "^0.12.5"}}