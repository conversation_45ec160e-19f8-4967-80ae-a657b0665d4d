// C<PERSON><PERSON> hình bảo mật cho Discord Remote Control Bot

module.exports = {
    // Danh sách các lệnh bị cấm (không phân biệt hoa thường)
    blockedCommands: [
        'format',
        'del',
        'rm -rf',
        'rmdir /s',
        'shutdown',
        'restart',
        'reboot',
        'halt',
        'poweroff',
        'mkfs',
        'fdisk',
        'diskpart',
        'reg delete',
        'net user',
        'net localgroup',
        'cacls',
        'icacls',
        'takeown'
    ],

    // Thời gian delay tối thiểu cho shutdown/restart (giây)
    minShutdownDelay: 5,

    // Timeout cho các lệnh command line (milliseconds)
    commandTimeout: 10000,

    // Độ dài tối đa của output (ký tự)
    maxOutputLength: 1900,

    // Danh sách User ID được phép sử dụng bot (để trống = tất cả)
    // Ví dụ: ['123456789012345678', '987654321098765432']
    allowedUsers: [],

    // Danh sách Server ID được phép sử dụng bot (để trống = tất cả)
    // Ví dụ: ['123456789012345678']
    allowedGuilds: [],

    // Có ghi log các lệnh được thực hiện không
    logCommands: true,

    // Có yêu cầu xác nhận cho các lệnh nguy hiểm không
    requireConfirmation: false,

    // Danh sách các extension file được phép chạy
    allowedFileExtensions: ['.bat', '.cmd', '.ps1', '.sh'],

    // Có cho phép chạy lệnh với quyền admin không
    allowAdminCommands: false
};
